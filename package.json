{"name": "tap2go", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-db": "node scripts/setup-database.js", "setup-users-admins": "node scripts/setup-users-admins.js", "setup-vendors": "node scripts/setup-vendors.js", "setup-restaurants": "node scripts/setup-restaurants.js", "setup-customers": "node scripts/setup-customers.js", "update-system-docs": "node scripts/update-system-docs.js", "add-restaurants": "node scripts/add-sample-restaurants.js", "clear-db": "node scripts/clear-database.js --confirm", "deploy-rules": "firebase deploy --only firestore:rules", "strapi:install": "node scripts/install-strapi-dependencies.js", "strapi:setup": "node scripts/setup-strapi.js", "strapi:dev": "cd tap2go-cms && npm run develop", "strapi:build": "cd tap2go-cms && npm run build", "strapi:start": "cd tap2go-cms && npm run start", "cms:cache-clear": "node scripts/clear-cms-cache.js", "cms:sync": "node scripts/sync-firebase-strapi.js", "neon:setup": "node scripts/setup-neon-database.js", "neon:test": "node -e \"require('./src/lib/neon/client.ts').neonClient.testConnection().then(r => console.log('Connected:', r))\"", "neon:info": "node -e \"require('./src/lib/neon/client.ts').neonClient.getDatabaseInfo().then(r => console.log(JSON.stringify(r, null, 2)))\""}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "@elastic/elasticsearch": "^9.0.2", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@opensearch-project/opensearch": "^3.5.1", "@reduxjs/toolkit": "^2.5.0", "@neondatabase/serverless": "^0.10.6", "axios": "^1.9.0", "cloudinary": "^2.6.1", "ioredis": "^5.4.1", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "reselect": "^5.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/google.maps": "^3.58.1", "@types/ioredis": "^5.0.0", "@types/node": "^20.17.57", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}